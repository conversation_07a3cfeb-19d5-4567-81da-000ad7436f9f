{"name": "wish", "private": true, "version": "0.0.0", "type": "module", "homepage": "https://Shiv16100.github.io/wish", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "gh-pages": "^6.3.0", "globals": "^16.3.0", "vite": "^7.0.4"}}