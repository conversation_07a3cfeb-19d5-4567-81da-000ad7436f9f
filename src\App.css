/* App Container */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.title-icon {
  color: #ffd700;
  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.5));
}

.subtitle {
  font-size: 1.2rem;
  margin: 10px 0 0 0;
  opacity: 0.9;
  font-weight: 300;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(220, 53, 69, 0.9);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  margin-top: 15px;
  font-size: 14px;
  backdrop-filter: blur(10px);
}

.info-message {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(23, 162, 184, 0.9);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  margin-top: 15px;
  font-size: 14px;
  backdrop-filter: blur(10px);
}



/* Add Wish Form */
.add-wish-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.input-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.wish-input, .category-input {
  flex: 1;
  min-width: 250px;
  padding: 15px 20px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: white;
}

.wish-input:focus, .category-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.wish-input {
  min-width: 300px;
}

.category-input {
  min-width: 200px;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 15px 25px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.add-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Filters */
.filters {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  flex-wrap: wrap;
  justify-content: center;
}

.filter-btn {
  padding: 12px 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.filter-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.filter-btn.active {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  border-color: white;
  font-weight: 600;
}

/* Wishes Container */
.wishes-container {
  min-height: 400px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: white;
}

.empty-icon {
  opacity: 0.6;
  margin-bottom: 20px;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.empty-state p {
  font-size: 1.1rem;
  opacity: 0.8;
  margin: 0;
}

/* Wishes Grid */
.wishes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

/* Wish Card */
.wish-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.wish-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.wish-card.priority {
  border-color: #ffd700;
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.2);
}

.wish-card.completed {
  opacity: 0.7;
  background: rgba(255, 255, 255, 0.8);
}

.wish-card.completed .wish-text {
  text-decoration: line-through;
  color: #666;
}

/* Wish Header */
.wish-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.wish-category {
  display: flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.wish-actions {
  display: flex;
  gap: 8px;
}

.priority-btn, .complete-btn, .delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.priority-btn {
  background: #f8f9fa;
  color: #6c757d;
}

.priority-btn:hover {
  background: #fff3cd;
  color: #856404;
}

.priority-btn.active {
  background: #ffd700;
  color: white;
}

.complete-btn {
  background: #f8f9fa;
  color: #6c757d;
}

.complete-btn:hover {
  background: #d1ecf1;
  color: #0c5460;
}

.complete-btn.active {
  background: #28a745;
  color: white;
}

.delete-btn {
  background: #f8f9fa;
  color: #6c757d;
}

.delete-btn:hover {
  background: #f5c6cb;
  color: #721c24;
}

/* Wish Content */
.wish-content {
  margin-top: 15px;
}

.wish-text {
  font-size: 16px;
  line-height: 1.5;
  margin: 0 0 12px 0;
  color: #333;
  font-weight: 500;
}

.wish-date {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
  opacity: 0.8;
}

/* Categories Section */
.categories-section {
  margin-top: 40px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 25px;
  color: white;
}

.categories-section h3 {
  margin: 0 0 15px 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.categories-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.category-tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 15px;
  }

  .title {
    font-size: 2rem;
  }

  .input-group {
    flex-direction: column;
  }

  .wish-input, .category-input {
    min-width: auto;
  }

  .wishes-grid {
    grid-template-columns: 1fr;
  }

  .filters {
    justify-content: flex-start;
  }

  .filter-btn {
    font-size: 14px;
    padding: 10px 16px;
  }
}

@media (max-width: 480px) {
  .add-wish-form {
    padding: 20px;
  }

  .wish-card {
    padding: 15px;
  }

  .title {
    font-size: 1.8rem;
  }

  .subtitle {
    font-size: 1rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.wish-card {
  animation: fadeIn 0.5s ease-out;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
